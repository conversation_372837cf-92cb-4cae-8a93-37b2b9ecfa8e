from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model

User = get_user_model()


class CompanyTemplate(models.Model):
    """Model for storing company details and template associations for invoice generation"""

    # Basic identification
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="company_templates"
    )
    template_name = models.CharField(
        max_length=255, help_text="User-defined name for this template"
    )
    template_id = models.CharField(
        max_length=100, help_text="ID of the selected Mizu template"
    )

    # Company details
    company_name = models.CharField(max_length=255)
    address_line_1 = models.Char<PERSON>ield(max_length=255, blank=True)
    address_line_2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state_province = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.Char<PERSON>ield(max_length=100, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    website = models.URLField(blank=True)

    # Business details
    default_payment_terms = models.CharField(max_length=100, default="Net 30 days")
    bank_name = models.CharField(max_length=255, blank=True)
    account_number = models.CharField(max_length=50, blank=True)
    routing_number = models.CharField(max_length=50, blank=True)
    swift_code = models.CharField(max_length=20, blank=True)
    tax_id = models.CharField(max_length=50, blank=True)
    business_registration = models.CharField(max_length=100, blank=True)

    # Logo handling (store as base64 or file path)
    logo_data = models.TextField(blank=True, help_text="Base64 encoded logo data")
    logo_filename = models.CharField(max_length=255, blank=True)

    # Metadata
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    last_used = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ["-last_used", "-updated_at"]
        unique_together = ["user", "template_name"]

    def __str__(self):
        return f"{self.template_name} - {self.company_name}"

    def mark_as_used(self):
        """Update the last_used timestamp"""
        self.last_used = timezone.now()
        self.save(update_fields=["last_used"])


class CSVUpload(models.Model):
    """Model for tracking CSV uploads and their processing status"""

    STATUS_CHOICES = [
        ("uploaded", "Uploaded"),
        ("processing", "Processing"),
        ("mapped", "Column Mapping Complete"),
        ("completed", "Invoice Generation Complete"),
        ("error", "Error"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="csv_uploads")
    company_template = models.ForeignKey(
        CompanyTemplate, on_delete=models.CASCADE, related_name="csv_uploads"
    )

    # File details
    filename = models.CharField(max_length=255)
    file_size = models.PositiveIntegerField()
    upload_path = models.CharField(max_length=512)

    # Processing details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="uploaded")
    columns_detected = models.JSONField(
        null=True, blank=True, help_text="List of column names from CSV"
    )
    column_mappings = models.JSONField(
        null=True, blank=True, help_text="Mapping of CSV columns to invoice fields"
    )
    sample_data = models.JSONField(
        null=True, blank=True, help_text="Sample rows from CSV for preview"
    )

    # Results
    total_rows = models.PositiveIntegerField(null=True, blank=True)
    invoices_generated = models.PositiveIntegerField(default=0)
    error_message = models.TextField(blank=True)

    # Timestamps
    uploaded_at = models.DateTimeField(default=timezone.now)
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ["-uploaded_at"]

    def __str__(self):
        return f"{self.filename} - {self.status}"
