from rest_framework import serializers
from .models import CompanyTemplate, CSVUpload


class CompanyTemplateSerializer(serializers.ModelSerializer):
    """Serializer for CompanyTemplate model"""
    
    class Meta:
        model = CompanyTemplate
        fields = [
            'id', 'template_name', 'template_id', 'company_name',
            'address_line_1', 'address_line_2', 'city', 'state_province',
            'postal_code', 'country', 'phone', 'email', 'website',
            'default_payment_terms', 'bank_name', 'account_number',
            'routing_number', 'swift_code', 'tax_id', 'business_registration',
            'logo_data', 'logo_filename', 'created_at', 'updated_at', 'last_used'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'last_used']
    
    def create(self, validated_data):
        # Set the user from the request context
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class CompanyTemplateListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing company templates"""
    
    class Meta:
        model = CompanyTemplate
        fields = [
            'id', 'template_name', 'template_id', 'company_name',
            'created_at', 'updated_at', 'last_used'
        ]


class CSVUploadSerializer(serializers.ModelSerializer):
    """Serializer for CSV upload tracking"""
    
    company_template_name = serializers.CharField(source='company_template.template_name', read_only=True)
    
    class Meta:
        model = CSVUpload
        fields = [
            'id', 'filename', 'file_size', 'status', 'columns_detected',
            'column_mappings', 'sample_data', 'total_rows', 'invoices_generated',
            'error_message', 'uploaded_at', 'processed_at', 'company_template_name'
        ]
        read_only_fields = [
            'id', 'uploaded_at', 'processed_at', 'columns_detected',
            'sample_data', 'total_rows', 'invoices_generated'
        ]


class ColumnMappingSerializer(serializers.Serializer):
    """Serializer for column mapping suggestions"""
    
    csv_columns = serializers.ListField(
        child=serializers.CharField(),
        help_text="List of column names from the uploaded CSV"
    )
    
    suggested_mappings = serializers.DictField(
        child=serializers.CharField(),
        help_text="Suggested mapping of invoice fields to CSV columns"
    )
    
    confidence_scores = serializers.DictField(
        child=serializers.FloatField(),
        help_text="Confidence scores for each mapping suggestion"
    )


class CSVProcessingSerializer(serializers.Serializer):
    """Serializer for CSV file processing"""
    
    file = serializers.FileField(help_text="CSV file to process")
    company_template_id = serializers.IntegerField(help_text="ID of the company template to use")
    
    def validate_file(self, value):
        """Validate that the uploaded file is a CSV"""
        if not value.name.lower().endswith('.csv'):
            raise serializers.ValidationError("Only CSV files are allowed.")
        
        # Check file size (limit to 10MB)
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError("File size cannot exceed 10MB.")
        
        return value
