from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from rest_framework import status, viewsets
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.renderers import StaticHT<PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from rest_framework.views import APIView
import os
from django.conf import settings
from .models import CompanyTemplate, CSVUpload
from .serializers import (
    CompanyTemplateSerializer,
    CompanyTemplateListSerializer,
    CSVUploadSerializer,
    ColumnMappingSerializer,
    CSVProcessingSerializer,
)
from .csv_processor import CSVProcessor

# Create your views here


@api_view(["POST"])
def extract_template_information(request):
    """
    Endpoint to extract information from an uploaded invoice PDF file.
    Returns dummy data for now.
    """
    if "file" not in request.FILES:
        return JsonResponse({"error": "No PDF file provided"}, status=400)

    pdf_file = request.FILES["file"]

    # Here we would process the PDF, but for now we return dummy data
    invoice_data = {
        "company_name": "Example Company Ltd",
        "address": "123 Business Street, City, Country",
        "contact_email": "<EMAIL>",
        "payment_terms": "Net 30 days",
        "bank_info": "Bank: Example Bank, Account: ********, Sort Code: 01-02-03",
        "company_logo": "base64_encoded_image_data_would_be_here",
        "Template Name": "",
    }

    return JsonResponse(invoice_data)


class InvoiceTemplateListView(APIView):
    """
    API endpoint to fetch all available invoice templates.
    Returns a list of templates with their metadata and raw HTML content.
    """

    permission_classes = [AllowAny]

    def get(self, request):
        templates_dir = os.path.join(settings.BASE_DIR, "invoice_templates")
        templates = []

        # Template metadata
        template_info = {
            "clean_business.html": {
                "name": "Clean Business",
                "description": "A clean and professional business invoice template",
                "preview_image": "https://placehold.co/300x400/059669/ffffff?text=Clean+Business",
            },
            "corporate.html": {
                "name": "Corporate",
                "description": "A formal corporate invoice template with traditional styling",
                "preview_image": "https://placehold.co/300x400/1f2937/ffffff?text=Corporate",
            },
            "minimalist.html": {
                "name": "Minimalist",
                "description": "A simple and elegant minimalist invoice template",
                "preview_image": "https://placehold.co/300x400/64748b/ffffff?text=Minimalist",
            },
            "elegant_classic.html": {
                "name": "Elegant Classic",
                "description": "An elegant classic invoice template with decorative elements",
                "preview_image": "https://placehold.co/300x400/d97706/ffffff?text=Elegant+Classic",
            },
            "contemporary.html": {
                "name": "Contemporary",
                "description": "A modern contemporary invoice template with vibrant colors",
                "preview_image": "https://placehold.co/300x400/7c3aed/ffffff?text=Contemporary",
            },
        }

        try:
            for filename in os.listdir(templates_dir):
                if filename.endswith(".html"):
                    file_path = os.path.join(templates_dir, filename)
                    with open(file_path, "r", encoding="utf-8") as file:
                        html_content = file.read()

                    template_data = {
                        "id": filename.replace(".html", ""),
                        "filename": filename,
                        "html_content": html_content,
                        **template_info.get(
                            filename,
                            {
                                "name": filename.replace(".html", "")
                                .replace("_", " ")
                                .title(),
                                "description": f"Invoice template: {filename}",
                                "preview_image": "/static/template-previews/default.png",
                            },
                        ),
                    }
                    templates.append(template_data)

            return Response(
                {"success": True, "templates": templates, "count": len(templates)}
            )

        except Exception as e:
            return Response({"success": False, "error": str(e)}, status=500)


class InvoiceTemplateDetailView(APIView):
    """
    API endpoint to fetch a specific invoice template by ID.
    Returns the raw HTML content using StaticHTMLRenderer.
    """

    renderer_classes = [StaticHTMLRenderer]
    permission_classes = [AllowAny]

    def get(self, request, template_id):
        templates_dir = os.path.join(settings.BASE_DIR, "invoice_templates")
        template_file = f"{template_id}.html"
        file_path = os.path.join(templates_dir, template_file)

        try:
            if not os.path.exists(file_path):
                return Response(
                    "<html><body><h1>Template not found</h1></body></html>", status=404
                )

            with open(file_path, "r", encoding="utf-8") as file:
                html_content = file.read()

            return Response(html_content)

        except Exception as e:
            return Response(
                f"<html><body><h1>Error loading template: {str(e)}</h1></body></html>",
                status=500,
            )


class CompanyTemplateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing company templates
    """

    serializer_class = CompanyTemplateSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return CompanyTemplate.objects.filter(user=self.request.user)

    def get_serializer_class(self):
        if self.action == "list":
            return CompanyTemplateListSerializer
        return CompanyTemplateSerializer

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    def perform_update(self, serializer):
        # Update the last_used timestamp when template is updated
        instance = serializer.save()
        instance.mark_as_used()

    @action(detail=True, methods=["post"])
    def mark_used(self, request, pk=None):
        """Mark a template as recently used"""
        template = self.get_object()
        template.mark_as_used()
        return Response({"status": "marked as used"})


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def upload_csv(request):
    """
    Upload and process a CSV file for invoice generation
    """
    serializer = CSVProcessingSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Get the company template
        company_template = CompanyTemplate.objects.get(
            id=serializer.validated_data["company_template_id"], user=request.user
        )

        # Initialize CSV processor
        processor = CSVProcessor()

        # Save the uploaded file
        uploaded_file = serializer.validated_data["file"]
        file_path = processor.save_uploaded_file(uploaded_file, request.user.id)

        # Process the CSV file
        csv_data = processor.process_csv_file(file_path)

        # Create CSV upload record
        csv_upload = CSVUpload.objects.create(
            user=request.user,
            company_template=company_template,
            filename=uploaded_file.name,
            file_size=uploaded_file.size,
            upload_path=file_path,
            columns_detected=csv_data["columns"],
            sample_data=csv_data["sample_data"],
            total_rows=csv_data["total_rows"],
            status="uploaded",
        )

        # Generate column mapping suggestions
        mapping_suggestions = processor.suggest_column_mappings(csv_data["columns"])

        return Response(
            {
                "upload_id": csv_upload.id,
                "csv_data": csv_data,
                "mapping_suggestions": mapping_suggestions,
            },
            status=status.HTTP_201_CREATED,
        )

    except CompanyTemplate.DoesNotExist:
        return Response(
            {"error": "Company template not found"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": f"Error processing CSV: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def validate_column_mapping(request):
    """
    Validate column mappings for CSV processing
    """
    upload_id = request.data.get("upload_id")
    mappings = request.data.get("mappings", {})

    if not upload_id:
        return Response(
            {"error": "upload_id is required"}, status=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Get the CSV upload record
        csv_upload = CSVUpload.objects.get(id=upload_id, user=request.user)

        # Initialize processor and validate mappings
        processor = CSVProcessor()
        validation_result = processor.validate_mappings(
            mappings, csv_upload.columns_detected
        )

        if validation_result["is_valid"]:
            # Save the mappings
            csv_upload.column_mappings = mappings
            csv_upload.status = "mapped"
            csv_upload.save()

        return Response(validation_result)

    except CSVUpload.DoesNotExist:
        return Response(
            {"error": "CSV upload not found"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": f"Error validating mappings: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
