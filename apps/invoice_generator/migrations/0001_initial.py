# Generated by Django 5.2 on 2025-05-30 15:29

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CompanyTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('template_name', models.Char<PERSON>ield(help_text='User-defined name for this template', max_length=255)),
                ('template_id', models.CharField(help_text='ID of the selected Mizu template', max_length=100)),
                ('company_name', models.CharField(max_length=255)),
                ('address_line_1', models.Char<PERSON>ield(blank=True, max_length=255)),
                ('address_line_2', models.CharField(blank=True, max_length=255)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('state_province', models.CharField(blank=True, max_length=100)),
                ('postal_code', models.CharField(blank=True, max_length=20)),
                ('country', models.CharField(blank=True, max_length=100)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('website', models.URLField(blank=True)),
                ('default_payment_terms', models.CharField(default='Net 30 days', max_length=100)),
                ('bank_name', models.CharField(blank=True, max_length=255)),
                ('account_number', models.CharField(blank=True, max_length=50)),
                ('routing_number', models.CharField(blank=True, max_length=50)),
                ('swift_code', models.CharField(blank=True, max_length=20)),
                ('tax_id', models.CharField(blank=True, max_length=50)),
                ('business_registration', models.CharField(blank=True, max_length=100)),
                ('logo_data', models.TextField(blank=True, help_text='Base64 encoded logo data')),
                ('logo_filename', models.CharField(blank=True, max_length=255)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_used', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='company_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-last_used', '-updated_at'],
                'unique_together': {('user', 'template_name')},
            },
        ),
        migrations.CreateModel(
            name='CSVUpload',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filename', models.CharField(max_length=255)),
                ('file_size', models.PositiveIntegerField()),
                ('upload_path', models.CharField(max_length=512)),
                ('status', models.CharField(choices=[('uploaded', 'Uploaded'), ('processing', 'Processing'), ('mapped', 'Column Mapping Complete'), ('completed', 'Invoice Generation Complete'), ('error', 'Error')], default='uploaded', max_length=20)),
                ('columns_detected', models.JSONField(blank=True, help_text='List of column names from CSV', null=True)),
                ('column_mappings', models.JSONField(blank=True, help_text='Mapping of CSV columns to invoice fields', null=True)),
                ('sample_data', models.JSONField(blank=True, help_text='Sample rows from CSV for preview', null=True)),
                ('total_rows', models.PositiveIntegerField(blank=True, null=True)),
                ('invoices_generated', models.PositiveIntegerField(default=0)),
                ('error_message', models.TextField(blank=True)),
                ('uploaded_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('company_template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='csv_uploads', to='invoice_generator.companytemplate')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='csv_uploads', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-uploaded_at'],
            },
        ),
    ]
