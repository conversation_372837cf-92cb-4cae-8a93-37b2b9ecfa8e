from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    extract_template_information,
    InvoiceTemplateListView,
    InvoiceTemplateDetailView,
    CompanyTemplateViewSet,
    upload_csv,
    validate_column_mapping,
)

# Create a router for the viewsets
router = DefaultRouter()
router.register(
    r"company-templates", CompanyTemplateViewSet, basename="company-template"
)

urlpatterns = [
    # Include router URLs for company templates
    path("", include(router.urls)),
    # Existing template endpoints
    path(
        "extract-template-information/",
        extract_template_information,
        name="extract_template_information",
    ),
    path(
        "templates/",
        InvoiceTemplateListView.as_view(),
        name="invoice_templates_list",
    ),
    path(
        "templates/<str:template_id>/",
        InvoiceTemplateDetailView.as_view(),
        name="invoice_template_detail",
    ),
    # CSV processing endpoints
    path(
        "csv-upload/",
        upload_csv,
        name="csv_upload",
    ),
    path(
        "validate-mapping/",
        validate_column_mapping,
        name="validate_column_mapping",
    ),
]
