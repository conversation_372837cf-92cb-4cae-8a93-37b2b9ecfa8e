import csv
import difflib
import os
from typing import Dict, List, Tuple, Any
from django.conf import settings


class CSVProcessor:
    """Utility class for processing CSV files and mapping columns"""
    
    # Standard invoice fields that we expect
    INVOICE_FIELDS = {
        'invoice_number': ['invoice', 'invoice_number', 'invoice_no', 'inv_no', 'number', '#'],
        'date': ['date', 'invoice_date', 'issue_date', 'created_date', 'bill_date'],
        'due_date': ['due_date', 'due', 'payment_due', 'expiry_date'],
        'customer_name': ['customer', 'client', 'customer_name', 'client_name', 'company', 'business'],
        'customer_email': ['email', 'customer_email', 'client_email', 'contact_email'],
        'customer_address': ['address', 'customer_address', 'client_address', 'billing_address'],
        'customer_phone': ['phone', 'customer_phone', 'client_phone', 'contact_phone'],
        'description': ['description', 'item', 'product', 'service', 'item_description', 'product_name'],
        'quantity': ['quantity', 'qty', 'amount', 'units'],
        'unit_price': ['price', 'unit_price', 'rate', 'cost', 'unit_cost'],
        'line_total': ['total', 'line_total', 'amount', 'subtotal'],
        'tax_rate': ['tax', 'tax_rate', 'vat', 'gst'],
        'tax_amount': ['tax_amount', 'tax_value', 'vat_amount'],
        'discount': ['discount', 'discount_amount', 'discount_percent'],
        'notes': ['notes', 'comments', 'remarks', 'memo'],
        'po_number': ['po', 'po_number', 'purchase_order', 'order_number'],
    }
    
    def __init__(self):
        self.confidence_threshold = 0.6  # Minimum confidence for auto-mapping
    
    def process_csv_file(self, file_path: str) -> Dict[str, Any]:
        """
        Process a CSV file and extract column information and sample data
        
        Args:
            file_path: Path to the CSV file
            
        Returns:
            Dictionary containing columns, sample data, and basic stats
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as csvfile:
                # Detect delimiter
                sample = csvfile.read(1024)
                csvfile.seek(0)
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter
                
                # Read CSV
                reader = csv.DictReader(csvfile, delimiter=delimiter)
                columns = reader.fieldnames
                
                # Get sample data (first 5 rows)
                sample_data = []
                total_rows = 0
                for i, row in enumerate(reader):
                    if i < 5:
                        sample_data.append(row)
                    total_rows += 1
                
                return {
                    'columns': columns,
                    'sample_data': sample_data,
                    'total_rows': total_rows,
                    'delimiter': delimiter
                }
                
        except Exception as e:
            raise Exception(f"Error processing CSV file: {str(e)}")
    
    def suggest_column_mappings(self, csv_columns: List[str]) -> Dict[str, Any]:
        """
        Suggest mappings between CSV columns and invoice fields using difflib
        
        Args:
            csv_columns: List of column names from the CSV file
            
        Returns:
            Dictionary with suggested mappings and confidence scores
        """
        suggested_mappings = {}
        confidence_scores = {}
        
        # Normalize CSV column names for better matching
        normalized_csv_columns = [col.lower().strip().replace(' ', '_') for col in csv_columns]
        
        for field, keywords in self.INVOICE_FIELDS.items():
            best_match = None
            best_score = 0
            best_original_column = None
            
            for i, normalized_col in enumerate(normalized_csv_columns):
                original_col = csv_columns[i]
                
                # Check for exact keyword matches first
                for keyword in keywords:
                    if keyword in normalized_col:
                        score = 1.0  # Perfect match
                        if score > best_score:
                            best_score = score
                            best_match = original_col
                            best_original_column = original_col
                        break
                
                # If no exact match, use difflib for fuzzy matching
                if best_score < 1.0:
                    for keyword in keywords:
                        similarity = difflib.SequenceMatcher(None, keyword, normalized_col).ratio()
                        if similarity > best_score and similarity > self.confidence_threshold:
                            best_score = similarity
                            best_match = original_col
                            best_original_column = original_col
            
            # Only suggest mappings above confidence threshold
            if best_score >= self.confidence_threshold:
                suggested_mappings[field] = best_match
                confidence_scores[field] = round(best_score, 2)
        
        return {
            'suggested_mappings': suggested_mappings,
            'confidence_scores': confidence_scores,
            'csv_columns': csv_columns
        }
    
    def validate_mappings(self, mappings: Dict[str, str], csv_columns: List[str]) -> Dict[str, Any]:
        """
        Validate that the provided mappings are valid
        
        Args:
            mappings: Dictionary mapping invoice fields to CSV columns
            csv_columns: List of available CSV columns
            
        Returns:
            Validation result with errors if any
        """
        errors = []
        warnings = []
        
        # Check that all mapped columns exist in CSV
        for field, column in mappings.items():
            if column and column not in csv_columns:
                errors.append(f"Column '{column}' mapped to '{field}' does not exist in CSV")
        
        # Check for required fields
        required_fields = ['invoice_number', 'customer_name', 'date']
        for field in required_fields:
            if field not in mappings or not mappings[field]:
                errors.append(f"Required field '{field}' is not mapped")
        
        # Check for duplicate mappings
        mapped_columns = [col for col in mappings.values() if col]
        duplicates = set([col for col in mapped_columns if mapped_columns.count(col) > 1])
        if duplicates:
            warnings.append(f"Columns mapped multiple times: {', '.join(duplicates)}")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def save_uploaded_file(self, uploaded_file, user_id: int) -> str:
        """
        Save uploaded CSV file to media directory
        
        Args:
            uploaded_file: Django UploadedFile object
            user_id: ID of the user uploading the file
            
        Returns:
            Path to the saved file
        """
        # Create user-specific directory
        upload_dir = os.path.join(settings.MEDIA_ROOT, 'csv_uploads', str(user_id))
        os.makedirs(upload_dir, exist_ok=True)
        
        # Generate unique filename
        import uuid
        file_extension = os.path.splitext(uploaded_file.name)[1]
        unique_filename = f"{uuid.uuid4().hex}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)
        
        # Save file
        with open(file_path, 'wb+') as destination:
            for chunk in uploaded_file.chunks():
                destination.write(chunk)
        
        return file_path
