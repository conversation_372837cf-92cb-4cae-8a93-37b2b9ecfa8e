import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { csvProcessingApi } from "../../../services/api";

const ColumnMapping = ({ variants, onComplete, uploadData }) => {
  const [mappings, setMappings] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [validationResult, setValidationResult] = useState(null);

  // Invoice fields that can be mapped
  const invoiceFields = [
    { id: 'invoice_number', label: 'Invoice Number', required: true },
    { id: 'date', label: 'Invoice Date', required: true },
    { id: 'customer_name', label: 'Customer Name', required: true },
    { id: 'due_date', label: 'Due Date', required: false },
    { id: 'customer_email', label: 'Customer Email', required: false },
    { id: 'customer_address', label: 'Customer Address', required: false },
    { id: 'customer_phone', label: 'Customer Phone', required: false },
    { id: 'description', label: 'Item Description', required: false },
    { id: 'quantity', label: 'Quantity', required: false },
    { id: 'unit_price', label: 'Unit Price', required: false },
    { id: 'line_total', label: 'Line Total', required: false },
    { id: 'tax_rate', label: 'Tax Rate', required: false },
    { id: 'tax_amount', label: 'Tax Amount', required: false },
    { id: 'discount', label: 'Discount', required: false },
    { id: 'notes', label: 'Notes', required: false },
    { id: 'po_number', label: 'PO Number', required: false },
  ];

  useEffect(() => {
    // Initialize mappings with suggestions from backend
    if (uploadData?.mappingSuggestions?.suggested_mappings) {
      setMappings(uploadData.mappingSuggestions.suggested_mappings);
    }
  }, [uploadData]);

  const handleMappingChange = (fieldId, columnName) => {
    setMappings(prev => ({
      ...prev,
      [fieldId]: columnName
    }));
    
    // Clear validation result when mappings change
    setValidationResult(null);
  };

  const validateMappings = async () => {
    try {
      setLoading(true);
      setError("");

      const response = await csvProcessingApi.validateMapping({
        upload_id: uploadData.uploadId,
        mappings: mappings
      });

      setValidationResult(response.data);
      
      if (response.data.is_valid) {
        // Proceed to next step if validation passes
        onComplete({
          uploadId: uploadData.uploadId,
          mappings: mappings,
          csvData: uploadData.csvData,
          companyTemplate: uploadData.companyTemplate
        });
      }

    } catch (err) {
      console.error("Mapping validation error:", err);
      setError(err.response?.data?.error || "Failed to validate mappings. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceColor = (fieldId) => {
    const confidence = uploadData?.mappingSuggestions?.confidence_scores?.[fieldId];
    if (!confidence) return 'text-gray-500';
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceText = (fieldId) => {
    const confidence = uploadData?.mappingSuggestions?.confidence_scores?.[fieldId];
    if (!confidence) return '';
    return `${Math.round(confidence * 100)}% confidence`;
  };

  const requiredFields = invoiceFields.filter(field => field.required);
  const optionalFields = invoiceFields.filter(field => !field.required);

  return (
    <motion.div
      className="flex flex-col gap-6 w-full"
      variants={variants}
    >
      <h2 className="text-xl font-medium text-gray-700 text-center mb-2">
        Map Your Data Columns
      </h2>

      <p className="text-gray-600 text-center max-w-3xl mx-auto">
        We've automatically suggested column mappings based on your CSV headers. 
        Review and adjust the mappings as needed.
      </p>

      {/* Company Template Info */}
      {uploadData?.companyTemplate && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium text-blue-900">{uploadData.companyTemplate.template_name}</h3>
              <p className="text-sm text-blue-700">{uploadData.companyTemplate.company_name}</p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Required Fields Section */}
        <div className="space-y-5">
          <h3 className="text-lg font-medium text-gray-700">Required Fields</h3>

          <div className="border rounded-lg bg-white p-6 shadow-sm space-y-4">
            {requiredFields.map(field => (
              <div key={field.id} className="flex flex-col space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  {field.label} <span className="text-red-500">*</span>
                </label>
                <select
                  className="border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-700"
                  value={mappings[field.id] || ""}
                  onChange={(e) => handleMappingChange(field.id, e.target.value)}
                >
                  <option value="" className="text-gray-700">Select column</option>
                  {uploadData?.csvData?.columns?.map((column, index) => (
                    <option key={index} value={column} className="text-gray-700">{column}</option>
                  ))}
                </select>
                {mappings[field.id] && (
                  <p className={`text-xs ${getConfidenceColor(field.id)}`}>
                    {getConfidenceText(field.id)}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Optional Fields Section */}
        <div className="lg:col-span-2 space-y-5">
          <h3 className="text-lg font-medium text-gray-700">Optional Fields</h3>

          <div className="border rounded-lg bg-white p-6 shadow-sm grid grid-cols-1 md:grid-cols-2 gap-4">
            {optionalFields.map(field => (
              <div key={field.id} className="flex flex-col space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  {field.label}
                </label>
                <select
                  className="border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-700"
                  value={mappings[field.id] || ""}
                  onChange={(e) => handleMappingChange(field.id, e.target.value)}
                >
                  <option value="" className="text-gray-700">Select column</option>
                  {uploadData?.csvData?.columns?.map((column, index) => (
                    <option key={index} value={column} className="text-gray-700">{column}</option>
                  ))}
                </select>
                {mappings[field.id] && (
                  <p className={`text-xs ${getConfidenceColor(field.id)}`}>
                    {getConfidenceText(field.id)}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Sample Data Preview */}
      {uploadData?.csvData?.sample_data && (
        <div className="mt-6 border rounded-lg overflow-hidden shadow-sm">
          <div className="bg-white py-4 px-6 border-b">
            <h3 className="text-lg font-medium text-gray-700">Sample Data Preview</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-white">
                <tr>
                  {uploadData.csvData.columns.map((column, index) => (
                    <th key={index} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {column}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {uploadData.csvData.sample_data.slice(0, 3).map((row, rowIndex) => (
                  <tr key={rowIndex}>
                    {uploadData.csvData.columns.map((column, colIndex) => (
                      <td key={colIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                        {row[column]}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Validation Results */}
      {validationResult && (
        <div className={`border rounded-lg p-4 ${
          validationResult.is_valid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
        }`}>
          <h3 className={`font-medium mb-2 ${
            validationResult.is_valid ? 'text-green-800' : 'text-red-800'
          }`}>
            Validation Results
          </h3>
          
          {validationResult.errors?.length > 0 && (
            <div className="mb-2">
              <p className="text-red-700 font-medium text-sm">Errors:</p>
              <ul className="text-red-600 text-sm list-disc list-inside">
                {validationResult.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
          
          {validationResult.warnings?.length > 0 && (
            <div>
              <p className="text-yellow-700 font-medium text-sm">Warnings:</p>
              <ul className="text-yellow-600 text-sm list-disc list-inside">
                {validationResult.warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </div>
          )}
          
          {validationResult.is_valid && (
            <p className="text-green-700 text-sm">All mappings are valid! Ready to generate invoices.</p>
          )}
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      <div className="flex justify-center mt-8">
        <button
          className={`py-4 px-10 rounded-lg font-medium text-lg ${
            loading ? 'bg-blue-500 text-white cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
          onClick={validateMappings}
          disabled={loading}
        >
          {loading ? (
            <div className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Validating...
            </div>
          ) : (
            "Validate & Generate Invoices"
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default ColumnMapping;
