import { motion } from "framer-motion";
import { useRef, useState } from "react";
import { csvProcessingApi } from "@/services/api";

const CSVUpload = ({ variants, onComplete, companyTemplate }) => {
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const fileInputRef = useRef(null);

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (!selectedFile) return;

    // Check if file is CSV
    if (!selectedFile.name.toLowerCase().endsWith('.csv')) {
      setError("Please upload a CSV file");
      return;
    }

    // Check file size (limit to 10MB)
    if (selectedFile.size > 10 * 1024 * 1024) {
      setError("File size cannot exceed 10MB");
      return;
    }

    setFile(selectedFile);
    setError("");
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) {
      // Simulate file input change
      const event = { target: { files: [droppedFile] } };
      handleFileChange(event);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const processFile = async () => {
    if (!file) {
      setError("Please select a file first");
      return;
    }

    if (!companyTemplate?.id) {
      setError("Company template is required");
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('company_template_id', companyTemplate.id);

      // Upload and process CSV
      const response = await csvProcessingApi.uploadCSV(formData);
      
      // Pass the response data to the next step
      onComplete({
        uploadId: response.data.upload_id,
        csvData: response.data.csv_data,
        mappingSuggestions: response.data.mapping_suggestions,
        companyTemplate: companyTemplate
      });

    } catch (err) {
      console.error("CSV processing error:", err);
      setError(err.response?.data?.error || "Failed to process the CSV file. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      className="flex flex-col items-center gap-6 w-full max-w-3xl mx-auto"
      variants={variants}
    >
      <h2 className="text-xl font-medium text-gray-700 text-center mb-2">
        Upload Your Sales Data
      </h2>

      <p className="text-gray-600 text-center max-w-2xl">
        Upload a CSV file containing your sales data. We'll help you map the columns to generate invoices using your selected template.
      </p>

      {/* Company Template Info */}
      {companyTemplate && (
        <div className="w-full max-w-2xl bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium text-blue-900">{companyTemplate.template_name}</h3>
              <p className="text-sm text-blue-700">{companyTemplate.company_name}</p>
            </div>
          </div>
        </div>
      )}

      <div className="w-full max-w-2xl">
        <div
          className="border-2 border-dashed border-gray-300 rounded-lg p-10 text-center cursor-pointer hover:border-blue-400 transition-colors"
          onClick={() => fileInputRef.current?.click()}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".csv"
            className="hidden"
          />

          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-600 mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>

          {file ? (
            <div>
              <p className="font-medium text-gray-800 text-xl mb-2">{file.name}</p>
              <p className="text-gray-600">{(file.size / 1024).toFixed(1)} KB</p>
            </div>
          ) : (
            <div>
              <p className="text-gray-700 text-xl font-medium mb-3">Click to select a CSV file</p>
              <p className="text-gray-600 text-md">or drag and drop here</p>
            </div>
          )}
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 mt-4">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        <button
          className={`mt-8 w-full py-4 rounded-lg font-medium text-lg ${file
            ? 'bg-blue-600 hover:bg-blue-700 text-white'
            : 'bg-gray-300 text-gray-600 cursor-not-allowed'}`}
          disabled={!file || loading}
          onClick={processFile}
        >
          {loading ? (
            <div className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing CSV...
            </div>
          ) : (
            "Process CSV File"
          )}
        </button>
      </div>

      {/* CSV Format Help */}
      <div className="w-full max-w-2xl bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="font-medium text-gray-700 mb-2">CSV Format Requirements</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Include column headers in the first row</li>
          <li>• Required fields: Invoice Number, Customer Name, Date</li>
          <li>• Optional fields: Email, Description, Quantity, Price, Total, etc.</li>
          <li>• Use standard date formats (YYYY-MM-DD, MM/DD/YYYY, etc.)</li>
          <li>• Maximum file size: 10MB</li>
        </ul>
      </div>
    </motion.div>
  );
};

export default CSVUpload;
